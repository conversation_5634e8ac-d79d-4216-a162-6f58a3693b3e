import React from 'react';
import { observer } from 'mobx-react-lite';
import { DeckStoreInstance } from '@/stores/DeckStore';
import { useStore } from '@/contexts/StoreContext';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Rota<PERSON><PERSON>cw, Zap, ZapOff } from 'lucide-react';

interface SyncButtonProps {
  deck: DeckStoreInstance;
}

const SyncButton: React.FC<SyncButtonProps> = observer(({ deck }) => {
  const { syncStore, settingsStore } = useStore();

  const handleSyncClick = () => {
    if (!deck.loadedTrack) return;
    
    // If sync is enabled, disable it
    if (deck.syncEnabled) {
      deck.setSyncEnabled(false);
      return;
    }
    
    // Try to sync to master
    const success = syncStore.syncDeckToMaster(deck.id);
    if (success) {
      deck.setSyncEnabled(true);
    }
  };

  const handleSyncOnce = () => {
    if (!deck.loadedTrack) return;
    
    // Perform one-time sync without enabling continuous sync
    syncStore.syncDeckToMaster(deck.id);
  };

  const canSync = () => {
    if (!deck.loadedTrack || deck.originalBpm <= 0) return false;
    
    // Can't sync if this deck is the master
    if (syncStore.masterDeck === deck) return false;
    
    // Need a master BPM to sync to
    const masterBpm = syncStore.getEffectiveMasterBpm();
    return masterBpm > 0;
  };

  const getSyncStatus = () => {
    if (!canSync()) {
      if (syncStore.masterDeck === deck) {
        return { text: "MASTER", variant: "default" as const, icon: null };
      }
      if (!deck.loadedTrack) {
        return { text: "NO TRACK", variant: "secondary" as const, icon: null };
      }
      if (deck.originalBpm <= 0) {
        return { text: "NO BPM", variant: "secondary" as const, icon: null };
      }
      return { text: "NO MASTER", variant: "secondary" as const, icon: null };
    }
    
    if (deck.syncEnabled) {
      return { text: "SYNC ON", variant: "default" as const, icon: <Zap className="h-3 w-3" /> };
    }
    
    return { text: "SYNC", variant: "outline" as const, icon: <ZapOff className="h-3 w-3" /> };
  };

  const getMasterBpmInfo = () => {
    const masterBpm = syncStore.getEffectiveMasterBpm();
    const masterDeck = syncStore.getMasterDeck();
    
    if (masterDeck) {
      return `Master: ${masterBpm.toFixed(1)} BPM (Deck ${masterDeck.id})`;
    } else if (syncStore.isUsingGhostMaster) {
      return `Ghost Master: ${masterBpm.toFixed(1)} BPM`;
    }
    
    return "No master reference";
  };

  const getSyncConstraintInfo = () => {
    if (!deck.loadedTrack || deck.originalBpm <= 0) return null;
    
    const masterBpm = syncStore.getEffectiveMasterBpm();
    if (masterBpm <= 0) return null;
    
    const targetRate = masterBpm / deck.originalBpm;
    const maxPitchRange = settingsStore.maxSyncPitchRange;
    
    if (maxPitchRange === "unlimited") {
      return `Target rate: ${targetRate.toFixed(2)}x (unlimited range)`;
    }
    
    const maxRange = parseInt(maxPitchRange) / 100;
    const minRate = 1 - maxRange;
    const maxRate = 1 + maxRange;
    const isInRange = targetRate >= minRate && targetRate <= maxRate;
    
    return `Target rate: ${targetRate.toFixed(2)}x (range: ${minRate.toFixed(2)}-${maxRate.toFixed(2)}) ${isInRange ? '✓' : '✗'}`;
  };

  const status = getSyncStatus();
  const constraintInfo = getSyncConstraintInfo();

  return (
    <div className="space-y-2">
      {/* Main Sync Button */}
      <div className="flex gap-2">
        <Button
          variant={status.variant}
          size="sm"
          onClick={handleSyncClick}
          disabled={!canSync() && syncStore.masterDeck !== deck}
          className="flex items-center gap-2 flex-1"
        >
          {status.icon}
          {status.text}
        </Button>
        
        {/* One-time Sync Button */}
        {canSync() && !deck.syncEnabled && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleSyncOnce}
            className="px-2"
            title="Sync once"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
        )}
      </div>

      {/* Sync Info */}
      <div className="text-xs text-muted-foreground space-y-1">
        <div>{getMasterBpmInfo()}</div>
        {constraintInfo && (
          <div className={constraintInfo.includes('✗') ? 'text-destructive' : ''}>
            {constraintInfo}
          </div>
        )}
        {deck.syncEnabled && (
          <Badge variant="secondary" className="text-xs">
            Sync Granularity: {settingsStore.syncGranularity}
          </Badge>
        )}
      </div>
    </div>
  );
});

export default SyncButton;
