## tasks
- Bugs
  - [ ] on load set crossfader value

- [ ] create and organize AllDecksStore - should include - 
  - [ ] numberOfDecks
  - [ ] activeDeck
  - [ ] decks object
  - [ ] cross faders
  - [ ] master volume
  - [ ] headphone volume
  - [ ] headphone monitoring
  - [ ] recording
  - [ ] sync - everything from syncStore

- [ ] Isolate AudioRoutingManager
    - [ ] does not need to know about anything outside of it - everything should be passed as parameters, especially the structure of the settingsStore. This will allow the manager to be used in other contexts, such as the audio diagnostic page.
    
- [ ] verify all settings global, controller and per deck are saved to DB and loaded on startup

- [X] auto master deck 
- [ ] sync all other decks to master

- [ ] ghost deck 
    - [ ] preserve bpm
    - [ ] preserve downbeat timing/phase alignment
    - [ ] allow other decks to continue syncing to the previous master's tempo and phase
    - [ ] have settings that decides in case no track is playing and a ghost master is used, if the ghost master should be used for sync or if sync should be disabled
    - [ ] have a STOP button that stops the ghost master, so a new track starts in its own BPM and play timing, and sync will sync to the new track

- [ ] organize settings
  - [ ] move settings from all decks page or from deck page to a settings page
  - [ ] move settings from mixer page to a settings page
  - [ ] move settings from library page to a settings page
  - [ ] setting page can have tabs based on the different settings categories

- [ ] outputs
  - [ ] user can now choose an output for the audio and headphones but it is ignored 
  - [ ] use process from gemini chat to fix that

- [ ] resets between plays - what settings should be kept when a track is replaced in deck
  - [ ] eq values
  - [ ] effects values

- [ ] looping functionality