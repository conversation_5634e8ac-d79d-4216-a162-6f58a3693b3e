/**
 * Test to verify SoundTouchJS migration is working correctly
 */

import { getSharedAudioContext } from '../AudioManager';
import { TimeStretchNode } from '../AudioRoutingManager';

export async function testSoundTouchMigration(): Promise<boolean> {
  try {
    const audioContext = getSharedAudioContext();
    
    // Load the SoundTouchJS worklet
    await audioContext.audioWorklet.addModule('/js/soundtouch-worklet.js');
    
    // Create a TimeStretchNode
    const timeStretchNode = new TimeStretchNode(audioContext);
    
    // Test parameter setting
    timeStretchNode.setPitchFactor(0.5); // Should set tempo=2.0, pitch=1.0
    
    // Test bypass functionality
    timeStretchNode.enableTimeStretching();
    timeStretchNode.disableTimeStretching();
    
    // Get debug info to verify everything is connected
    const debugInfo = timeStretchNode.getDebugInfo();
    
    console.log('SoundTouch Migration Test Results:', {
      soundTouchConnected: debugInfo.soundTouchConnected,
      currentPitchFactor: debugInfo.currentPitchFactor,
      isBypassed: debugInfo.isBypassed,
      gainCompensation: debugInfo.gainCompensation
    });
    
    // Clean up
    timeStretchNode.dispose();
    
    return debugInfo.soundTouchConnected;
  } catch (error) {
    console.error('SoundTouch Migration Test Failed:', error);
    return false;
  }
}

/**
 * Test SoundTouchJS parameter mapping
 */
export function testParameterMapping(): void {
  console.log('Testing SoundTouchJS Parameter Mapping:');
  
  // Test cases for Master Tempo functionality
  const testCases = [
    { pitchFactor: 1.0, expectedTempo: 1.0, expectedPitch: 1.0 },
    { pitchFactor: 0.5, expectedTempo: 2.0, expectedPitch: 1.0 }, // 2x speed
    { pitchFactor: 2.0, expectedTempo: 0.5, expectedPitch: 1.0 }, // 0.5x speed
    { pitchFactor: 0.8, expectedTempo: 1.25, expectedPitch: 1.0 }, // 1.25x speed
  ];
  
  testCases.forEach(({ pitchFactor, expectedTempo, expectedPitch }) => {
    const playbackRate = 1.0 / pitchFactor;
    console.log(`pitchFactor: ${pitchFactor} → tempo: ${playbackRate}, pitch: ${expectedPitch}`);
    
    if (Math.abs(playbackRate - expectedTempo) > 0.001) {
      console.error(`❌ Mapping error for pitchFactor ${pitchFactor}`);
    } else {
      console.log(`✅ Correct mapping for pitchFactor ${pitchFactor}`);
    }
  });
}
