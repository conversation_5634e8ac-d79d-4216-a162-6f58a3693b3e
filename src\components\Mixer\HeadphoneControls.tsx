// src/components/Mixer/HeadphoneControls.tsx

import React from 'react';
import { observer } from 'mobx-react-lite';
import { useStore } from '../../contexts/StoreContext';
import { Card } from '../ui/card';
import { Switch } from '../ui/switch';
import { Label } from '../ui/label';
import { Fader } from '../ui/fader';
import { cn } from '../../lib/utils';

export const HeadphoneControls: React.FC = observer(() => {
  const rootStore = useStore();
  const { settingsStore } = rootStore;

  const handleHeadphoneVolumeChange = (value: number) => {
    settingsStore.setHeadphoneVolume(value);
    
    // Update the audio routing manager
    try {
      const headphonePath = rootStore.audioRoutingManager.getHeadphonePath();
      headphonePath.setHeadphoneVolume(value);
    } catch (error) {
      console.error('Failed to update headphone volume:', error);
    }
  };

  const handleMasterHeadphoneMonitoringChange = (enabled: boolean) => {
    settingsStore.setMasterHeadphoneMonitoring(enabled);
    
    // Update the audio routing manager
    try {
      if (enabled) {
        rootStore.audioRoutingManager.enableMasterHeadphoneMonitoring();
      } else {
        rootStore.audioRoutingManager.disableMasterHeadphoneMonitoring();
      }
    } catch (error) {
      console.error('Failed to update master headphone monitoring:', error);
    }
  };

  const handleDeckHeadphoneMonitoringChange = (deckId: number, enabled: boolean) => {
    settingsStore.setDeckHeadphoneMonitoring(deckId, enabled);
    
    // Update the audio routing manager
    try {
      if (enabled) {
        rootStore.audioRoutingManager.enableHeadphoneMonitoring(deckId);
      } else {
        rootStore.audioRoutingManager.disableHeadphoneMonitoring(deckId);
      }
    } catch (error) {
      console.error(`Failed to update deck ${deckId} headphone monitoring:`, error);
    }
  };

  return (
    <Card className={cn("p-4 rounded-lg shadow-md bg-card text-card-foreground flex flex-col space-y-4 w-full max-w-md mx-auto")}>
      <div className="w-full text-center">
        <h3 className="text-lg font-semibold">Headphone Controls</h3>
      </div>

      {/* Headphone Volume */}
      <div className="space-y-2">
        <Label htmlFor="headphone-volume" className="text-sm font-medium">
          Headphone Volume
        </Label>
        <div className="px-2">
          <Fader
            id="headphone-volume"
            orientation="horizontal"
            value={settingsStore.headphoneVolume}
            onChange={handleHeadphoneVolumeChange}
            defaultValue={1}
            min={0}
            max={1}
            step={0.01}
            className="w-full"
            enableDoubleClick={true}
          />
        </div>
        <div className="text-xs text-muted-foreground text-center">
          {Math.round(settingsStore.headphoneVolume * 100)}%
        </div>
      </div>

      {/* Master Monitoring */}
      <div className="flex items-center justify-between">
        <Label htmlFor="master-headphone-monitoring" className="text-sm font-medium">
          Monitor Master
        </Label>
        <Switch
          id="master-headphone-monitoring"
          checked={settingsStore.masterHeadphoneMonitoring}
          onCheckedChange={handleMasterHeadphoneMonitoringChange}
        />
      </div>

      {/* Deck Monitoring */}
      <div className="space-y-2">
        <Label className="text-sm font-medium">Monitor Decks</Label>
        <div className="space-y-2">
          {rootStore.decks.map((deck) => {
            const isMonitoring = settingsStore.deckHeadphoneMonitoring.get(deck.id) || false;
            
            return (
              <div key={deck.id} className="flex items-center justify-between">
                <Label 
                  htmlFor={`deck-${deck.id}-monitoring`} 
                  className="text-sm"
                >
                  Deck {deck.id}
                  {deck.loadedTrack && (
                    <span className="text-xs text-muted-foreground ml-1">
                      ({deck.loadedTrack.title})
                    </span>
                  )}
                </Label>
                <Switch
                  id={`deck-${deck.id}-monitoring`}
                  checked={isMonitoring}
                  onCheckedChange={(enabled) => handleDeckHeadphoneMonitoringChange(deck.id, enabled)}
                  disabled={!deck.loadedTrack}
                />
              </div>
            );
          })}
        </div>
      </div>

      {/* Status Indicator */}
      <div className="text-xs text-muted-foreground text-center pt-2 border-t">
        {(() => {
          const monitoringDecks = Array.from(settingsStore.deckHeadphoneMonitoring.entries())
            .filter(([_, enabled]) => enabled)
            .map(([deckId, _]) => deckId);
          
          const parts = [];
          if (settingsStore.masterHeadphoneMonitoring) {
            parts.push('Master');
          }
          if (monitoringDecks.length > 0) {
            parts.push(`Deck${monitoringDecks.length > 1 ? 's' : ''} ${monitoringDecks.join(', ')}`);
          }
          
          return parts.length > 0 
            ? `Monitoring: ${parts.join(' + ')}`
            : 'No monitoring active';
        })()}
      </div>
    </Card>
  );
});

export default HeadphoneControls;
