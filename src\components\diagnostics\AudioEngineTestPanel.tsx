import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { getSharedAudioContext } from '@/services/AudioManager';
import { testSoundTouchVolumeDrop } from '@/services/tests/AudioEngineTest';

const AudioEngineTestPanel: React.FC = () => {
  const [testResult, setTestResult] = useState<string | null>(null);
  const [isRunning, setIsRunning] = useState(false);

  const runVolumeTest = async () => {
    setIsRunning(true);
    setTestResult('Running test...');
    
    try {
      const audioContext = getSharedAudioContext();
      const dbDifference = await testSoundTouchVolumeDrop(audioContext);
      
      setTestResult(`Volume drop: ${dbDifference.toFixed(2)} dB`);
      
      // Calculate the gain compensation factor
      const gainCompensation = Math.pow(10, Math.abs(dbDifference) / 20);
      setTestResult(prev => 
        `${prev}\nRecommended gain compensation factor: ${gainCompensation.toFixed(2)}`
      );
    } catch (error) {
      console.error('Test failed:', error);
      setTestResult(`Test failed: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsRunning(false);
    }
  };

  return (
    <div className="p-4 border rounded-md">
      <h2 className="text-lg font-semibold mb-4">Audio Engine Test Panel</h2>
      
      <div className="space-y-4">
        <div>
          <Button 
            onClick={runVolumeTest} 
            disabled={isRunning}
            variant="outline"
          >
            {isRunning ? 'Running...' : 'Test Phase Vocoder Volume Drop'}
          </Button>
        </div>
        
        {testResult && (
          <div className="p-3 bg-muted rounded-md whitespace-pre-line">
            <h3 className="font-medium mb-2">Test Results:</h3>
            {testResult}
          </div>
        )}
      </div>
    </div>
  );
};

export default AudioEngineTestPanel;