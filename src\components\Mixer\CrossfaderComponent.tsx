import React from 'react';
import { observer } from 'mobx-react-lite';
import { useStore } from '../../contexts/StoreContext';
import { Fader } from '../../components/ui/fader';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { Switch } from '../../components/ui/switch';
import { Label } from '../../components/ui/label';
import { cn } from '@/lib/utils';
import { availableCurves } from '../../utils/crossfaderCurves'; // For SVG generation

interface CurveVisualProps {
  curveName: string;
  width?: number;
  height?: number;
}

const CurveVisual: React.FC<CurveVisualProps> = observer(({ curveName, width = 100, height = 50 }) => {
  const selectedCurveObject = availableCurves.find(c => c.name === curveName);
  if (!selectedCurveObject) return null;

  const curveFunc = selectedCurveObject.curve;
  const points = 50; // Number of points to plot for the curve

  let pathLeft = `M 0 ${height - curveFunc(0)[0] * height}`;
  let pathRight = `M 0 ${height - curveFunc(0)[1] * height}`;

  for (let i = 1; i <= points; i++) {
    const x = i / points;
    const [gainLeft, gainRight] = curveFunc(x);
    pathLeft += ` L ${x * width} ${height - gainLeft * height}`;
    pathRight += ` L ${x * width} ${height - gainRight * height}`;
  }

  return (
    <svg width={width} height={height} viewBox={`0 0 ${width} ${height}`} className="border rounded-md">
      <path d={pathLeft} stroke="rgba(255, 165, 0, 0.7)" fill="none" strokeWidth="2" />
      <path d={pathRight} stroke="rgba(0, 128, 255, 0.7)" fill="none" strokeWidth="2" />
    </svg>
  );
});


export const CrossfaderComponent: React.FC = observer(() => {
  const { settingsStore, syncStore } = useStore();
  const is4BandMode = settingsStore.eqBands === "4-band";

  const handleFaderChange = (value: number) => {
    settingsStore.setCrossfaderValue(value);

    // Monitor crossfader changes for auto master switching
    if (syncStore) {
      syncStore.monitorCrossfaderChange(value);
    }
  };

  const handleCurveChange = (newCurveName: string) => {
    settingsStore.setCrossfaderCurve(newCurveName);
  };

  const handleEnablePerBandCrossfaders = (checked: boolean) => {
    settingsStore.setEnablePerBandCrossfaders(checked);
  };

  const handleLowCrossfaderChange = (value: number) => {
    settingsStore.setLowCrossfaderValue(value);
  };

  const handleMidCrossfaderChange = (value: number) => {
    settingsStore.setMidCrossfaderValue(value);
  };

  const handleMidLoCrossfaderChange = (value: number) => {
    settingsStore.setMidLoCrossfaderValue(value);
  };

  const handleMidHiCrossfaderChange = (value: number) => {
    settingsStore.setMidHiCrossfaderValue(value);
  };

  const handleHighCrossfaderChange = (value: number) => {
    settingsStore.setHighCrossfaderValue(value);
  };

  return (
    <div className={cn("p-4 rounded-lg shadow-md bg-card text-card-foreground flex flex-col items-center space-y-4 w-full max-w-md mx-auto")}>
      <div className="w-full text-center">
        <h3 className="text-lg font-semibold">Crossfader</h3>
      </div>

      <div className="w-full px-4">
        <Fader
          orientation="horizontal"
          defaultValue={0.5}
          value={settingsStore.crossfaderValue}
          onChange={handleFaderChange}
          min={0}
          max={1}
          step={0.01}
          className="w-full"
          enableDoubleClick={true}
          disabled={settingsStore.numberOfDecks < 2}
        />
      </div>

      <div className="flex flex-row justify-around items-center w-full space-x-4">
        <div className="flex flex-col items-center space-y-2">
          <span className="text-sm font-medium">Curve: {settingsStore.crossfaderCurve}</span>
          <Select value={settingsStore.crossfaderCurve} onValueChange={handleCurveChange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select curve" />
            </SelectTrigger>
            <SelectContent>
              {settingsStore.availableCrossfaderCurves.map((curveName) => (
                <SelectItem key={curveName} value={curveName}>
                  {curveName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex flex-col items-center space-y-1">
           <span className="text-xs text-muted-foreground">Preview</span>
          <CurveVisual curveName={settingsStore.crossfaderCurve} />
        </div>
      </div>

      <div className="flex items-center space-x-2 pt-2">
        <Switch
          id="enable-per-band-crossfaders"
          checked={settingsStore.enablePerBandCrossfaders}
          onCheckedChange={handleEnablePerBandCrossfaders}
          disabled={settingsStore.numberOfDecks < 2}
        />
        <Label htmlFor="enable-per-band-crossfaders">Enable per-band crossfaders</Label>
      </div>

      {settingsStore.enablePerBandCrossfaders && (
        <div className="w-full space-y-4 pt-2">
          <div className="space-y-1">
            <Label className="text-sm font-medium">Low</Label>
            <Fader
              orientation="horizontal"
              defaultValue={0.5}
              value={settingsStore.lowCrossfaderValue}
              onChange={handleLowCrossfaderChange}
              min={0}
              max={1}
              step={0.01}
              className="w-full"
              enableDoubleClick={true}
              disabled={settingsStore.numberOfDecks < 2}
              color="hsl(var(--destructive))" // Red color for low frequencies
            />
          </div>

          {is4BandMode ? (
            <>
              <div className="space-y-1">
                <Label className="text-sm font-medium">Mid-Low</Label>
                <Fader
                  orientation="horizontal"
                  defaultValue={0.5}
                  value={settingsStore.midLoCrossfaderValue}
                  onChange={handleMidLoCrossfaderChange}
                  min={0}
                  max={1}
                  step={0.01}
                  className="w-full"
                  enableDoubleClick={true}
                  disabled={settingsStore.numberOfDecks < 2}
                  color="hsl(var(--amber-500))" // Amber color for mid-low frequencies
                />
              </div>

              <div className="space-y-1">
                <Label className="text-sm font-medium">Mid-High</Label>
                <Fader
                  orientation="horizontal"
                  defaultValue={0.5}
                  value={settingsStore.midHiCrossfaderValue}
                  onChange={handleMidHiCrossfaderChange}
                  min={0}
                  max={1}
                  step={0.01}
                  className="w-full"
                  enableDoubleClick={true}
                  disabled={settingsStore.numberOfDecks < 2}
                  color="hsl(var(--lime-500))" // Lime color for mid-high frequencies
                />
              </div>
            </>
          ) : (
            <div className="space-y-1">
              <Label className="text-sm font-medium">Mid</Label>
              <Fader
                orientation="horizontal"
                defaultValue={0.5}
                value={settingsStore.midCrossfaderValue}
                onChange={handleMidCrossfaderChange}
                min={0}
                max={1}
                step={0.01}
                className="w-full"
                enableDoubleClick={true}
                disabled={settingsStore.numberOfDecks < 2}
                color="hsl(var(--chart-4))" // Yellow/green for mid frequencies
              />
            </div>
          )}

          <div className="space-y-1">
            <Label className="text-sm font-medium">High</Label>
            <Fader
              orientation="horizontal"
              defaultValue={0.5}
              value={settingsStore.highCrossfaderValue}
              onChange={handleHighCrossfaderChange}
              min={0}
              max={1}
              step={0.01}
              className="w-full"
              enableDoubleClick={true}
              disabled={settingsStore.numberOfDecks < 2}
              color="hsl(var(--blue-500))" // Blue color for high frequencies
            />
          </div>
        </div>
      )}
    </div>
  );
});

// Export for potential use in stories or tests, not strictly necessary for the app
export default CrossfaderComponent;
