import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { useStore } from '@/contexts/StoreContext';

interface AudioRoutingDebuggerProps {
  deckId?: number;
}

export const AudioRoutingDebugger: React.FC<AudioRoutingDebuggerProps> = observer(({ deckId = 1 }) => {
   const { audioRoutingManager } = useStore();
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const refreshDebugInfo = async () => {
    setIsRefreshing(true);
    try {
      const info = audioRoutingManager.getCompleteAudioRoutingDebugInfo();
      setDebugInfo(info);
    } catch (error) {
      console.error('Failed to get debug info:', error);
    }
    setIsRefreshing(false);
  };

  const testAudioRouting = async () => {
    try {
      await audioRoutingManager.testDeckAudioRouting(deckId);
      // Refresh debug info after test
      setTimeout(refreshDebugInfo, 1500);
    } catch (error) {
      console.error('Failed to test audio routing:', error);
    }
  };

  const testSoundTouch = async () => {
    try {
      const deckPath = await audioRoutingManager.getDeckPath(deckId);
      if (deckPath) {
        await deckPath.testSoundTouch();
        // Refresh debug info after test
        setTimeout(refreshDebugInfo, 3000);
      }
    } catch (error) {
      console.error('Failed to test SoundTouch:', error);
    }
  };

  const reconnectTimeStretch = async () => {
    try {
      const deckPath = await audioRoutingManager.getDeckPath(deckId);
      if (deckPath) {
        deckPath.reconnectTimeStretch();
        // Refresh debug info after reconnection
        setTimeout(refreshDebugInfo, 500);
      }
    } catch (error) {
      console.error('Failed to reconnect time stretch:', error);
    }
  };

  const visualizeAudioGraph = () => {
    audioRoutingManager.visualizeAudioGraph();
  };

  useEffect(() => {
    refreshDebugInfo();
  }, []);

  const renderDebugInfo = (obj: any, depth = 0): React.ReactNode => {
    if (obj === null || obj === undefined) return 'null';
    if (typeof obj === 'boolean') return obj ? 'true' : 'false';
    if (typeof obj === 'number') return obj.toFixed(3);
    if (typeof obj === 'string') return obj;
    if (Array.isArray(obj)) {
      return (
        <ul style={{ marginLeft: depth * 20 }}>
          {obj.map((item, index) => (
            <li key={index}>{renderDebugInfo(item, depth + 1)}</li>
          ))}
        </ul>
      );
    }
    if (typeof obj === 'object') {
      return (
        <div style={{ marginLeft: depth * 20 }}>
          {Object.entries(obj).map(([key, value]) => (
            <div key={key} style={{ marginBottom: 4 }}>
              <strong>{key}:</strong> {renderDebugInfo(value, depth + 1)}
            </div>
          ))}
        </div>
      );
    }
    return String(obj);
  };

  return (
    <div style={{ 
      padding: 20, 
      border: '1px solid #ccc', 
      borderRadius: 8, 
      backgroundColor: '#f9f9f9',
      fontFamily: 'monospace',
      fontSize: 12
    }}>
      <h3>Audio Routing Debugger - Deck {deckId}</h3>
      
      <div style={{ marginBottom: 20 }}>
        <button 
          onClick={refreshDebugInfo} 
          disabled={isRefreshing}
          style={{ marginRight: 10, padding: '8px 16px' }}
        >
          {isRefreshing ? 'Refreshing...' : 'Refresh Debug Info'}
        </button>
        
        <button
          onClick={testAudioRouting}
          style={{ marginRight: 10, padding: '8px 16px' }}
        >
          Test Audio Routing
        </button>

        <button
          onClick={testSoundTouch}
          style={{ marginRight: 10, padding: '8px 16px' }}
        >
          Test SoundTouch
        </button>

        <button
          onClick={reconnectTimeStretch}
          style={{ marginRight: 10, padding: '8px 16px' }}
        >
          Reconnect TimeStretch
        </button>

        <button
          onClick={visualizeAudioGraph}
          style={{ padding: '8px 16px' }}
        >
          Visualize Audio Graph (Console)
        </button>
      </div>

      {debugInfo && (
        <div style={{ 
          backgroundColor: 'white', 
          padding: 15, 
          borderRadius: 4,
          maxHeight: 600,
          overflow: 'auto'
        }}>
          <h4>Debug Information:</h4>
          {renderDebugInfo(debugInfo)}
        </div>
      )}

      <div style={{ marginTop: 20, fontSize: 11, color: '#666' }}>
        <p><strong>Instructions:</strong></p>
        <ul>
          <li><strong>Refresh Debug Info:</strong> Updates the current state of all audio routing</li>
          <li><strong>Test Audio Routing:</strong> Injects a test tone into the deck's audio path</li>
          <li><strong>Test SoundTouch:</strong> Injects a test tone directly into the SoundTouch processor</li>
          <li><strong>Reconnect TimeStretch:</strong> Forces reconnection of the TimeStretch input (fixes stale connections)</li>
          <li><strong>Visualize Audio Graph:</strong> Prints a visual representation to the console</li>
        </ul>
        <p><strong>Key things to check:</strong></p>
        <ul>
          <li>timeStretch.soundTouchConnected should be true</li>
          <li>timeStretch.audioGraphConnected should be true</li>
          <li>timeStretch.hasAudioSignal should be true when audio is playing</li>
          <li>When Master Tempo is ON: wetGain = 1.0, bypassGain = 0.0</li>
          <li>When Master Tempo is OFF: wetGain = 0.0, bypassGain = 1.0</li>
        </ul>
      </div>
    </div>
  );
});
