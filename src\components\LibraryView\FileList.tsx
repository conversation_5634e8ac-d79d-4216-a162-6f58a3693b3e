import React, { useMemo } from 'react';
import { observer } from 'mobx-react-lite';
// Import TrackedDirectoryModel as well
import { LibraryStoreInstance, TrackInfoModel, TrackedDirectoryModel } from '@/stores/LibraryStore';
import { Instance } from 'mobx-state-tree';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils"; // For conditional styling
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from "@/components/ui/context-menu"; // Import context menu components
import { useStore } from "@/contexts/StoreContext"; // Import useStore hook

interface FileListProps {
  selectedDirectoryPath: string | null;
  libraryStore: LibraryStoreInstance;
  selectedFileId: string | null;
  onSelectFile: (fileId: string | null) => void;
}

// Helper to format duration (seconds to mm:ss)
const formatDuration = (seconds: number | undefined | null): string => {
  if (seconds === undefined || seconds === null || isNaN(seconds)) {
    return '--:--';
  }
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

const FileList: React.FC<FileListProps> = observer(({
  selectedDirectoryPath,
  libraryStore,
  selectedFileId,
  onSelectFile
}) => {
  const rootStore = useStore(); // Access the root store


  const filteredTracks = useMemo((): Instance<typeof TrackInfoModel>[] => {
    if (!selectedDirectoryPath || selectedDirectoryPath === "Local Music") {
      return []; // Nothing selected or root selected
    }

    // Parse the path: "Local Music/TrackedDirName/Sub/Folder"
    const pathParts = selectedDirectoryPath.split('/').slice(1); // Remove "Local Music"
    if (pathParts.length === 0) return []; // Should not happen if path is not "Local Music"

    const trackedDirName = pathParts[0];
    const subFolderPath = pathParts.slice(1).join('/'); // Rejoin the rest, or empty string if none

    // Find the tracked directory ID by name - Add type for 'dir'
    const trackedDir = libraryStore.trackedDirectoriesList.find((dir: Instance<typeof TrackedDirectoryModel>) => dir.name === trackedDirName);
    if (!trackedDir) {
      console.warn(`Could not find tracked directory ID for name: ${trackedDirName}`);
      return [];
    }
    const dirId = trackedDir.id;
    const dirPrefix = `${dirId}:`;

    // Add type for 'track'
    return libraryStore.tracksList.filter((track: Instance<typeof TrackInfoModel>) => {
      if (!track.id.startsWith(dirPrefix)) {
        return false; // Belongs to a different tracked directory
      }

      // Get the relative path within the tracked directory
      const trackRelativePath = track.filePath; // filePath is already relative
      const trackPathSegments = trackRelativePath.split('/').filter(Boolean);
      const trackDirPath = trackPathSegments.slice(0, -1).join('/'); // Directory part of the track's path

      // Check if the track's directory path matches the selected subfolder path
      // Check if the track's directory path matches the selected subfolder path
      return trackDirPath === subFolderPath;
    });
    // Removed Debugging Logs

    // The filtering logic is already performed by the initial .filter() call
    // We just need to return the result of that.
    const filteredTracksResult = libraryStore.tracksList.filter((track: Instance<typeof TrackInfoModel>) => {
        if (!track.id.startsWith(dirPrefix)) {
          return false; // Belongs to a different tracked directory
        }
        const trackRelativePath = track.filePath;
        const trackPathSegments = trackRelativePath.split('/').filter(Boolean);
        const trackDirPath = trackPathSegments.slice(0, -1).join('/');
        return trackDirPath === subFolderPath;
      });

    return filteredTracksResult; // Return the calculated tracks

  }, [selectedDirectoryPath, libraryStore.tracksList, libraryStore.trackedDirectoriesList]);

  if (!selectedDirectoryPath || selectedDirectoryPath === "Local Music") {
      return <div className="p-4 text-center text-muted-foreground">Select a folder to view files.</div>;
  }

  if (filteredTracks.length === 0) {
      return <div className="p-4 text-center text-muted-foreground">No music files found in this folder.</div>;
  }

  return (
    <ScrollArea className="h-full">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Title</TableHead>
            <TableHead>Artist</TableHead>
            <TableHead>Album</TableHead>
            <TableHead className="text-right">Duration</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredTracks.map((track) => (
            <ContextMenu key={track.id}>
              <ContextMenuTrigger asChild>
                <TableRow
                  onClick={() => onSelectFile(track.id)}
                  className={cn(
                    "cursor-pointer",
                    selectedFileId === track.id && "bg-accent" // Highlight selected row
                  )}
                >
                  <TableCell className="font-medium truncate" title={track.title ?? track.filename}>
                    {track.title ?? track.filename}
                  </TableCell>
                  <TableCell className="truncate" title={track.artist ?? ''}>{track.artist ?? '-'}</TableCell>
                  <TableCell className="truncate" title={track.album ?? ''}>{track.album ?? '-'}</TableCell>
                  <TableCell className="text-right">{formatDuration(track.duration)}</TableCell>
                </TableRow>
              </ContextMenuTrigger>
              <ContextMenuContent>
                {/* Add options for each deck */}
                {rootStore.decks.map((deck) => (
                  <ContextMenuItem
                    key={deck.id}
                    onClick={() => {
                      // Call the loadTrack action on the specific deck
                      deck.loadTrack(track.id);
                      // Optionally select the file when loading to deck
                      onSelectFile(track.id);
                    }}
                  >
                    Load to Deck {deck.id}
                  </ContextMenuItem>
                ))}
              </ContextMenuContent>
            </ContextMenu>
          ))}
        </TableBody>
      </Table>
    </ScrollArea>
  );
});

export default FileList;
