import { types, Instance, getRoot } from "mobx-state-tree";
import { DeckStoreInstance, DeckStoreModel } from "./DeckStore";
import { RootStoreType } from "./RootStore";

export const SyncStoreModel = types
  .model("SyncStore", {
    // Current master deck (null if no master or using ghost master)
    masterDeck: types.maybeNull(types.reference(DeckStoreModel)),
    // Ghost master tempo for when no deck is master
    ghostMasterBpm: types.optional(types.number, 120),
    // Ghost master phase/downbeat timing preservation
    ghostMasterPhase: types.optional(types.number, 0),
    // Audio level monitoring for auto master selection
    audioLevelMonitoringEnabled: types.optional(types.boolean, true),
  })
  .volatile<{
    // Fader monitoring for auto master switching
    faderMonitoringTimers: Map<number, NodeJS.Timeout>;
    // Crossfader monitoring for auto master switching
    crossfaderMonitoringTimer: NodeJS.Timeout | undefined;
  }>(() => ({
    faderMonitoringTimers: new Map(),
    crossfaderMonitoringTimer: undefined,
  }))
  .actions((self) => {
    // Set a deck as master (manual selection)
    const setMasterDeck = (deck: DeckStoreInstance | null) => {
      // if current master is already deckId return - nothing to do
      if (self.masterDeck === deck) return;

      // Clear current master and preserve ghost master state
      if (self.masterDeck) {
        const currentMaster = self.masterDeck;
        if (currentMaster) {
          currentMaster.setIsMaster(false);
          // Preserve ghost master BPM and phase when transitioning to ghost master
          if (deck) {
            self.ghostMasterBpm = currentMaster.currentBpm;
            self.ghostMasterPhase = currentMaster.currentTime % (60 / currentMaster.currentBpm); // Current beat phase
          }
        }
      }

      // Set new master
      self.masterDeck = deck;
      if (deck) {
        const newMaster = deck;
        if (newMaster) {
          newMaster.setIsMaster(true);
          // Update ghost master BPM to match new master for future fallback
          if (newMaster.currentBpm > 0) {
            self.ghostMasterBpm = newMaster.currentBpm;
            self.ghostMasterPhase = newMaster.currentTime % (60 / newMaster.currentBpm);
          }
        }
      }

      console.log(`****************************************`);
      console.log(`Master deck set to: ${deck?.id || 'none (ghost master)'}`);
      console.log(`****************************************`);
    };

    // Get the current master deck instance
    const getMasterDeck = (): DeckStoreInstance | null => {
      return self.masterDeck;
    };

    // Get the effective master BPM (from master deck or ghost master)
    const getEffectiveMasterBpm = (): number => {
      const masterDeck = getMasterDeck();
      return masterDeck?.currentBpm || self.ghostMasterBpm;
    };

    // Check if a deck is suitable to be master
    const isDeckSuitableForMaster = (deck: DeckStoreInstance): boolean => {

      if (!deck.loadedTrack || !deck.isPlaying) return false;
      if (deck.currentBpm <= 0) return false; // No valid BPM data

      // Check remaining time (>10 seconds OR looping)
      const remainingTime = deck.effectiveDuration - deck.currentTime;
      if (remainingTime < 10) {
        // Check if track is looping - for now we'll assume no looping
        // TODO: Add loop functionality to deck store and check here
        // if (deck.isLooping && deck.loopLength >= 60 / deck.currentBpm) return true; // At least one bar
        return false;
      }

      return true;
    };

    // aim to check if current master can stay as master
    // uses following checks from isDeckSuitableForMaster:
    // is deck playing
    // checks for time left 
    // check if looping
    // And adds:
    // check for volume
    // check for crossfader side
    const canDeckStayAsMaster = (deck: DeckStoreInstance): boolean => {
      if(!isDeckSuitableForMaster(deck)) return false;

      // Check volume is above 50%
      if (deck.volume < 0.5) return false;

      // Check crossfader side
      const rootStore = getRoot<RootStoreType>(self);
      const crossfaderValue = rootStore.settingsStore.crossfaderValue;

      if (crossfaderValue > 0.7 && deck.side === 'left') return false;
      if (crossfaderValue < 0.3 && deck.side === 'right') return false;

      return true;
    };

    // Find the best candidate for master deck based on audio levels
    const findBestMasterCandidate = (): DeckStoreInstance | null => {
      console.log('Finding best master candidate...');
      const rootStore = getRoot<RootStoreType>(self);
      const suitableDecks = rootStore.decks.filter(isDeckSuitableForMaster);
      
      if (suitableDecks.length === 0) return null;
      
      // Find deck with highest audio level
      let bestDeck = suitableDecks[0];
      let highestLevel = bestDeck.audioEngine?.getAnalyzer()?.getAverageRMS() || 0;

      for (const deck of suitableDecks.slice(1)) {
        const level = deck.audioEngine?.getAnalyzer()?.getAverageRMS() || 0;
        if (level > highestLevel) {
          highestLevel = level;
          bestDeck = deck;
        }
      }
      
      return bestDeck;
    };

    // Auto master mode logic - called when deck states change
    const updateAutoMaster = (): DeckStoreInstance | null => {
      console.log('Auto master update triggered...');
      const rootStore = getRoot<RootStoreType>(self);
      if (!rootStore.settingsStore.autoMasterMode) return null;
      
      const currentMaster = getMasterDeck();
      
      // If no master, try to find one
      if (!currentMaster) {
        const candidate = findBestMasterCandidate();
        if (candidate) {
          setMasterDeck(candidate);
        }
        return candidate || null;
      }

      // if current deck still qualifies to stay as master does not change it
      if(canDeckStayAsMaster(currentMaster)) {
        return currentMaster;
      }
      
      // find best master
      const candidate = findBestMasterCandidate();
      setMasterDeck(candidate || null);
      return candidate || null;
    };

    // Monitor deck fader changes for auto master switching
    const monitorDeckFaderChange = (deckId: number, faderValue: number) => {
      const rootStore = getRoot<RootStoreType>(self);
      if (!rootStore.settingsStore.autoMasterMode) return;

      // Clear existing timer for this deck
      const existingTimer = self.faderMonitoringTimers.get(deckId);
      if (faderValue >= 0.5) {
        if(existingTimer) {
          console.log(`Fader for ${deckId} raised above 50%, clearing master switch timer`);
          clearTimeout(existingTimer);
        }
        return;
      }

      // Check if current master's fader is lowered below 50%
      const currentMaster = getMasterDeck();
      if (currentMaster && currentMaster.id === deckId && currentMaster.isPlaying && faderValue < 0.5) {
        console.log(`Current master ${deckId} fader lowered below 50%, starting timer at ${Date.now()}`);
        // Start debounced timer to check for master switch
        const timer = setTimeout(() => {
          // checks the current master is still master
          if (self.masterDeck !== currentMaster) return;

            console.log(`Auto master switch: Current master ${deckId} fader lowered below 50%, checking for new master... at ${Date.now()}`);
            // @ts-ignore - needs to be called on self, as otherwise it is not considered an action and cannot make the change
            self.updateAutoMaster();
        }, rootStore.settingsStore.autoMasterSwitchDelay);

        self.faderMonitoringTimers.set(deckId, timer);
      }
    };

    // Monitor crossfader changes for auto master switching
    const monitorCrossfaderChange = (crossfaderValue: number) => {
      console.log("Crossfader changed to", crossfaderValue);
      const rootStore = getRoot<RootStoreType>(self);
      if (!rootStore.settingsStore.autoMasterMode) return;

      // Check if crossfader is moved more than 70% towards one side
      const currentMaster = getMasterDeck();
      if (currentMaster && (crossfaderValue > 0.7 || crossfaderValue < 0.3)) {
        // Determine which side the crossfader favors
        const favoredSide = crossfaderValue > 0.7 ? 'right' : 'left';
        // check if currentMaster is in the favored side
        if (favoredSide === currentMaster.side) {
          if(self.crossfaderMonitoringTimer){
            console.log("Crossfader moved to favored side of current master, clearing timer");
            clearTimeout(self.crossfaderMonitoringTimer);
          }
          return;
        }

        console.log("Crossfader moved to", favoredSide, "side, starting timer at ", Date.now());
       
        self.crossfaderMonitoringTimer = setTimeout(() => {
          // checks the current master is still master
          if (self.masterDeck !== currentMaster) return;

          console.log(`Auto master switch: Crossfader moved to ${favoredSide} side, checking for new master... at ${Date.now()}`);
          // @ts-ignore - needs to be called on self, as otherwise it is not considered an action and cannot make the change
          self.updateAutoMaster();
        }, rootStore.settingsStore.autoMasterSwitchDelay);
      }
    };

    // Stop ghost master functionality
    const stopGhostMaster = () => {
      console.log('Ghost master stopped - next playing track will become master without syncing to ghost');
      self.ghostMasterPhase = -1;
      // Clear ghost master state but keep it available for reference
      // The next track that starts playing will become master without syncing to ghost
      // uses the phase=-1 as an indicator ghost is not in use
    };

    // Handle new track starting - check if it should sync to ghost or become master
    const handleNewTrackStart = (deck: DeckStoreInstance) => {
      const rootStore = getRoot<RootStoreType>(self);
      if (!rootStore.settingsStore.autoMasterMode) return;

      if (!deck) return;

      const currentMaster = getMasterDeck();

      // If no master (using ghost master)
      if (!currentMaster) {
        // Check if new track should sync to ghost or become master
        if (rootStore.settingsStore.newTrackSyncToGhost) {
          // Sync to ghost master
          const ghostBpm = self.ghostMasterBpm;
          if (ghostBpm > 0 && deck.originalBpm > 0) {
            const targetRate = ghostBpm / deck.originalBpm;
            // check targetRate is within max pitch range
            const maxPitchRange = rootStore.settingsStore.maxSyncPitchRange;
            if (maxPitchRange !== "unlimited") {
              const maxRange = parseInt(maxPitchRange) / 100; // Convert percentage to decimal
              const minRate = 1 - maxRange;
              const maxRate = 1 + maxRange;
              
              if (targetRate >= minRate && targetRate <= maxRate) {
                deck.setPlaybackRate(targetRate);
                console.log(`New track ${deck.id} synced to ghost master at ${ghostBpm} BPM`);
              } else {
                console.warn(`Sync failed: Target rate ${targetRate.toFixed(2)} outside allowed range [${minRate.toFixed(2)}, ${maxRate.toFixed(2)}]`);
              }
            }
          }
        }
        // New track becomes master
        setMasterDeck(deck);
        console.log(`New track ${deck.id} became master`);
      } else {
        // There's already a master, trigger normal auto master logic
        updateAutoMaster();
      }
    };

    // Sync a deck to the current master
    const syncDeckToMaster = (deckId: number) => {
      const rootStore = getRoot<RootStoreType>(self);
      const deck = rootStore.decks.find(d => d.id === deckId);
      if (!deck || !deck.loadedTrack) return false;
      
      const masterBpm = getEffectiveMasterBpm();
      if (masterBpm <= 0) return false;
      
      // Check sync constraints
      const maxPitchRange = rootStore.settingsStore.maxSyncPitchRange;
      const targetRate = masterBpm / deck.originalBpm;
      
      if (maxPitchRange !== "unlimited") {
        const maxRange = parseInt(maxPitchRange) / 100; // Convert percentage to decimal
        const minRate = 1 - maxRange;
        const maxRate = 1 + maxRange;
        
        if (targetRate < minRate || targetRate > maxRate) {
          console.warn(`Sync failed: Target rate ${targetRate.toFixed(2)} outside allowed range [${minRate.toFixed(2)}, ${maxRate.toFixed(2)}]`);
          return false;
        }
      }
      
      // Apply BPM sync
      deck.setPlaybackRate(targetRate);
      
      // TODO: Implement phase alignment based on syncGranularity setting
      // This would require beat/bar position tracking and alignment logic
      
      console.log(`Deck ${deckId} synced to master BPM ${masterBpm} (rate: ${targetRate.toFixed(2)})`);
      return true;
    };

    const updateBPM = (deck: DeckStoreInstance) => {
      if (deck.isMaster) {
        self.ghostMasterBpm = deck.currentBpm;
        // update each deck that is not the deck in parameters
        const rootStore = getRoot<RootStoreType>(self);
        rootStore.decks.forEach(d => {
          if (d !== deck && d.syncEnabled) {
            d.setPlaybackRate(deck.currentBpm / d.originalBpm);
          }
        });
      }
    };

    return {
      setMasterDeck,
      getMasterDeck,
      getEffectiveMasterBpm,
      isDeckSuitableForMaster,
      canDeckStayAsMaster,
      findBestMasterCandidate,
      updateAutoMaster,
      monitorDeckFaderChange,
      monitorCrossfaderChange,
      stopGhostMaster,
      handleNewTrackStart,
      syncDeckToMaster,
      updateBPM,
    };
  })
  .views((self) => ({
    get hasMasterDeck() {
      return self.masterDeck !== null;
    },
    
    get isUsingGhostMaster() {
      return self.masterDeck === null;
    },
    //  returns the current master bpm and if no master the ghost BPM
    get getBpm(): number {
      return self.masterDeck?.currentBpm || self.ghostMasterBpm;
    }
  }));

export interface SyncStoreInstance extends Instance<typeof SyncStoreModel> {}
