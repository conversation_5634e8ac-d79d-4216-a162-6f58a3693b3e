import { types, flow, Instance } from "mobx-state-tree"; // Add Instance
import { LibraryStoreModel } from "./LibraryStore";
import { UiStoreModel } from "./UiStore";
import { SettingsStoreModel } from "./SettingsStore";
import { db, AppDatabase } from '../services/DatabaseService'; // Import db instance and type
import { AudioAnalysisService } from '../services/AudioAnalysisService'; // Import AudioAnalysisService
import { ExternalAnalysisService } from '../services/ExternalAnalysisService'; // Import ExternalAnalysisService
import { AudioRoutingManager } from '../services/AudioRoutingManager'; // Import AudioRoutingManager
import { DeckStoreModel } from "./DeckStore"; // Import DeckStoreModel
import { SyncStoreModel } from "./SyncStore"; // Import SyncStoreModel
import { audioPermissionManager } from '../services/AudioPermissionManager'; // Import audio permission manager

export const RootStoreModel = types
  .model("RootStore", {
    libraryStore: types.optional(LibraryStoreModel, {}),
    uiStore: types.optional(UiStoreModel, {}),
    settingsStore: types.optional(SettingsStoreModel, {}),
    syncStore: types.optional(SyncStoreModel, {}),
    decks: types.array(DeckStoreModel),
    isHydrated: types.optional(types.boolean, false),
    audioPermissionGranted: types.optional(types.boolean, false),
    audioServicesInitialized: types.optional(types.boolean, false),
  })
  .volatile<{
      _databaseService: AppDatabase | null;
      _audioAnalysisService: AudioAnalysisService | null; // Add volatile property for AudioAnalysisService
      _externalAnalysisService: ExternalAnalysisService | null; // Add volatile property for ExternalAnalysisService
      _audioRoutingManager: AudioRoutingManager | null; // Add volatile property for AudioRoutingManager
  }>(() => ({ // Define volatile state
    _databaseService: null,
    _audioAnalysisService: null, // Initialize volatile property
    _externalAnalysisService: null, // Initialize volatile property
    _audioRoutingManager: null, // Initialize volatile property
  }))
  .actions((self) => ({
    updateEQFrequencies() {
      self._audioRoutingManager?.updateEQFrequencies();
    },

    updateDeckInstances: flow(function* (newNumberOfDecks: 1 | 2 | 4) {
      const currentNumberOfDecks = self.decks.length;

      if (newNumberOfDecks > currentNumberOfDecks) {
        for (let i = currentNumberOfDecks+1; i <= newNumberOfDecks; i++) {
          const newDeck = DeckStoreModel.create({ id: i, side: i%2 === 1 ? 'left' : 'right'});
          self.decks.push(newDeck);
        }
        console.log(`Added ${newNumberOfDecks - currentNumberOfDecks} deck(s). Total: ${newNumberOfDecks}`);
      } else if (newNumberOfDecks < currentNumberOfDecks) {
        const decksToRemove = currentNumberOfDecks - newNumberOfDecks;
        // Optionally, you can call a cleanup method on each deck before removing it
        // for (let i = currentNumberOfDecks - 1; i >= newNumberOfDecks; i--) {
        //   const deckToRemove = self.decks[i];
        //   deckToRemove.beforeDestroy();
        // }
        self.decks.splice(newNumberOfDecks, decksToRemove); // Remove decks from the end
        console.log(`Removed ${decksToRemove} deck(s). Total: ${newNumberOfDecks}`);
      }
      // No action if numbers are the same
    }),

    // Initialize audio engines for all decks
    initializeDeckAudioEngines: flow(function* () {
      console.log("Initializing deck audio engines...");
      for (const deck of self.decks) {
        try {
          yield deck.initAudioEngine();
        } catch (error) {
          console.error(`Failed to initialize audio engine for deck ${deck.id}:`, error);
        }
      }
      console.log("Deck audio engines initialization complete");
    })
  }))
  .actions((self) => ({
    // Initialize audio services after permission is granted
    initializeAudioServices: flow(function* () {
      if (self.audioServicesInitialized) {
        console.log("Audio services already initialized");
        return Promise.resolve();
      }

      if (!audioPermissionManager.isPermissionGranted()) {
        console.error("Cannot initialize audio services without permission");
        return Promise.resolve();
      }

      try {
        console.log("Initializing audio services...");

        // Initialize AudioAnalysisService
        (self as Instance<typeof RootStoreModel>)._audioAnalysisService = new AudioAnalysisService(self as RootStoreType);

        // Initialize ExternalAnalysisService
        (self as Instance<typeof RootStoreModel>)._externalAnalysisService = new ExternalAnalysisService(self as RootStoreType);

        // Initialize AudioRoutingManager
        (self as Instance<typeof RootStoreModel>)._audioRoutingManager = new AudioRoutingManager(self as RootStoreType);

        // Initialize the routing manager
        if (self._audioRoutingManager) {
          yield self._audioRoutingManager.initialize();
        }

        self.audioServicesInitialized = true;
        self.audioPermissionGranted = true;

        console.log("Audio services initialized successfully");

        // Initialize deck audio engines now that audio services are ready
        yield self.initializeDeckAudioEngines();

        // If stores are already hydrated, start audio analysis
        if (self.isHydrated && self._audioAnalysisService) {
          console.log("RootStore: Starting audio analysis after audio services initialization.");
          yield self._audioAnalysisService.startAnalysis();
        }
      } catch (error) {
        console.error("Failed to initialize audio services:", error);
        throw error;
      }
    }),

    // Hydrate all stores that need it
    hydrateStores: flow(function* () {
      // Hydrate settings first as other hydration steps might depend on it
      yield self.settingsStore.hydrateFromDb();

      // Now that settings are loaded, update deck instances
      // The numberOfDecks from settingsStore is the source of truth
      yield self.updateDeckInstances(self.settingsStore.numberOfDecks);

      // Hydrate other stores
      yield Promise.all([
        self.libraryStore.hydrateFromDb(),
        self.uiStore.hydrateFromDb(),
        // Add other hydration calls here if they are async and don't depend on settingsStore.numberOfDecks
      ]);

      // After all stores are hydrated and decks are initialized, restore deck states
      for (const deck of self.decks) {
        yield deck.loadDeckState();
      }

      self.isHydrated = true;
      console.log("RootStore hydration complete.");

      // Only start audio analysis if audio services are already initialized
      if (self.audioServicesInitialized && self._audioAnalysisService) {
          console.log("RootStore: Calling startAnalysis after hydration.");
          yield self._audioAnalysisService.startAnalysis();
      } else {
          console.log("RootStore: Audio services not yet initialized, skipping audio analysis start.");
      }
    }),

    // Save all application state
    saveAppState: flow(function* () {
      console.log("Saving application state...");

      // Save UI state
      yield self.uiStore.saveAppState();

      // Save deck states
      for (const deck of self.decks) {
        yield deck.saveDeckState();
      }

      console.log("Application state saved successfully");
    }),
  }))
  .actions((self) => ({
    // Use afterCreate to assign the db instance to volatile state
    afterCreate: flow(function* () {
      // Assign the imported db instance to the volatile property
      // We need to cast self here because volatile properties aren't directly on the typed self in actions
      (self as Instance<typeof RootStoreModel>)._databaseService = db;

      // Audio services will be initialized later when permission is granted
      // Do not initialize audio services here to comply with browser autoplay policies

      // Deck initialization is now handled in hydrateStores after settings are loaded.

      // Add beforeunload event handler to save state and prevent accidental navigation/closing
      window.addEventListener('beforeunload', (e: BeforeUnloadEvent) => {
        // Save application state before unloading
        self.saveAppState();

        // In production, show a confirmation dialog
        if (process.env.NODE_ENV !== 'development') {
          // Cancel the event and show confirmation dialog
          e.preventDefault();
          // Set a return value for browsers that require it
          const message = 'Music playback will stop if you leave. Are you sure?';
          // Note: returnValue is deprecated but still needed for some browsers
          e.returnValue = message;
          return message;
        }

        // disposes of the audio manager
        self._audioRoutingManager?.dispose();
      });

      // Automatically trigger hydration after the root store is created
      self.hydrateStores();

      const pathname = location.pathname;

    // If we're at the root path, use saved route (if available)
    if (pathname === '/') {
      if (self.uiStore.currentRoute && self.uiStore.currentRoute !== '/') {
        // navigate the app to the self.uiStore.currentRoute
        console.log('Navigating to saved route:', self.uiStore.currentRoute);
        window.location.href = self.uiStore.currentRoute;
      }
    } else {
      // Otherwise, update the current route in the store
      self.uiStore.setCurrentRoute(pathname);
    }
    }),

  }))
  .views((self) => ({
    // Example view combining info from multiple stores
    // get appStatus() {
    //   return `Hydrated: ${self.isHydrated}, Scan Status: ${self.libraryStore.scanStatus}`;
    // }
    // Provide access to the DatabaseService instance via a view
    get databaseService(): AppDatabase {
        // Since afterCreate runs immediately, _databaseService should be set.
        // Add a check just in case, though it shouldn't happen in normal flow.
        if (!self._databaseService) {
            console.error("DatabaseService accessed before RootStore.afterCreate completed!");
            // Return db directly as a fallback, or throw an error
            return db; // Or throw new Error("DatabaseService not initialized");
        }
        return self._databaseService;
    },
    // Provide access to the AudioAnalysisService instance via a view
    get audioAnalysisService(): AudioAnalysisService {
        if (!self._audioAnalysisService) {
            if (!self.audioPermissionGranted) {
                throw new Error("AudioAnalysisService not available - audio permission not granted");
            }
            throw new Error("AudioAnalysisService not initialized");
        }
        return self._audioAnalysisService;
    },
    // Provide access to the ExternalAnalysisService instance via a view
    get externalAnalysisService(): ExternalAnalysisService {
        if (!self._externalAnalysisService) {
            if (!self.audioPermissionGranted) {
                throw new Error("ExternalAnalysisService not available - audio permission not granted");
            }
            throw new Error("ExternalAnalysisService not initialized");
        }
        return self._externalAnalysisService;
    },

    // Provide access to the AudioRoutingManager instance via a view
    get audioRoutingManager(): AudioRoutingManager {
        if (!self._audioRoutingManager) {
            if (!self.audioPermissionGranted) {
                throw new Error("AudioRoutingManager not available - audio permission not granted");
            }
            throw new Error("AudioRoutingManager not initialized");
        }
        return self._audioRoutingManager;
    },

    // Safe access to audio services that returns null if not available
    get audioAnalysisServiceSafe(): AudioAnalysisService | null {
        return self._audioAnalysisService;
    },
    get externalAnalysisServiceSafe(): ExternalAnalysisService | null {
        return self._externalAnalysisService;
    },
    get audioRoutingManagerSafe(): AudioRoutingManager | null {
        return self._audioRoutingManager;
    }
  }));

// Define the type for the RootStore instance
export type RootStoreType = Instance<typeof RootStoreModel>; // Use Instance for type


// Define the type for the RootStore snapshot
// Using a simpler approach to avoid type errors
export type RootStoreSnapshotType = any;
