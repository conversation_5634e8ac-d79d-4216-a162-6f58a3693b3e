import React from 'react';
import { observer } from 'mobx-react-lite';
import { useStore } from '@/contexts/StoreContext'; // Corrected to useStore as per existing
import { useParams, Navigate } from 'react-router-dom'; // Added Navigate
import DeckComponent from '../Deck/DeckComponent';
import { Link } from 'react-router-dom'; // Added Link for the "not found" message

const DeckPage: React.FC = observer(() => {
  const { deckId } = useParams<{ deckId?: string }>();
  const { decks, settingsStore } = useStore(); // Assuming useStore provides rootStore or similar structure

  // If no deckId is provided in the URL, redirect to the first available deck, or library if no decks
  if (!deckId) {
    if (decks.length > 0) {
      return <Navigate to={`/deck/${decks[0].id}`} replace />;
    }
    // If settingsStore indicates 0 decks, or no decks array, handle appropriately
    if (decks.length === 0) {
        return (
            <div className="container mx-auto p-4 text-center">
                <h1 className="text-2xl font-bold mb-4">No Decks Active</h1>
                <p>There are currently no decks active. Please <Link to="/settings" className="text-blue-500 hover:underline">go to settings</Link> to activate at least one deck.</p>
            </div>
        );
    }
    // Fallback if decks exist but something else went wrong (should be covered by first case)
    return <Navigate to="/library" replace />;
  }

  const selectedDeckId = parseInt(deckId, 10);

  const deck = decks.find(d => d.id === selectedDeckId);

  if (!deck) {
    const totalDecksActive = settingsStore.numberOfDecks;

    let message = `Deck "${selectedDeckId}" not found.`;
    if (selectedDeckId > totalDecksActive) {
        message = `Deck "${selectedDeckId}" is not available because only ${totalDecksActive} deck(s) are currently active.`;
    }

    return (
      <div className="container mx-auto p-4 text-center">
        <h1 className="text-2xl font-bold mb-4">Deck Not Found</h1>
        <p>{message}</p>
        <p>Please <Link to="/settings" className="text-blue-500 hover:underline">check your settings</Link> or select an active deck.</p>
        {decks.length > 0 && (
          <div className="mt-4">
            <p>Active decks:</p>
            <ul className="list-none p-0">
              {decks.map(d => (
                <li key={d.id}><Link to={`/deck/${d.id}`} className="text-blue-500 hover:underline">{d.id}</Link></li>
              ))}
            </ul>
          </div>
        )}
      </div>
    );
  }

  // Deck is found, render the DeckComponent
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Deck View - {selectedDeckId}</h1>
      <div className="grid grid-cols-1 gap-4">
        <DeckComponent deck={deck} />
      </div>
    </div>
  );
});

export default DeckPage;
